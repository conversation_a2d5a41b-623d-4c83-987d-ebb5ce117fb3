class_name PlayerInputSystem
extends Node

@export var movement_system: MovementSystem

var _player_movement_component: MovementComponent
var _player_ability_component: AbilityComponent
var _player_input_component: InputComponent

var _current_direction: Vector2 = Vector2.ZERO
var _hold_timer: float = 0.0
var _repeat_mode_active: bool = false
var _movement_cooldown: float = 0.0

func _ready() -> void:
	_find_player_component.call_deferred()

func _find_player_component() -> void:
	var player_nodes: Array[Node] = get_tree().get_nodes_in_group(&"player")
	if not player_nodes.is_empty():
		var player_node: Node = player_nodes[0]
		_player_movement_component = player_node.find_child(&"MovementComponent", true, false)
		_player_ability_component = player_node.find_child(&"AbilityComponent", true, false)
		_player_input_component = player_node.find_child(&"InputComponent", true, false)


func _process(delta: float) -> void:
	if not is_instance_valid(_player_movement_component) or not is_instance_valid(movement_system) or not is_instance_valid(_player_input_component) or not is_instance_valid(_player_input_component.data):
		return

	var input_vector: Vector2 = Input.get_vector(&"move_left", &"move_right", &"move_up", &"move_down")
	var new_direction: Vector2 = _get_dominant_direction(input_vector)

	new_direction *= _get_inversion_multiplier()

	if new_direction != _current_direction:
		_current_direction = new_direction
		_hold_timer = 0.0
		_repeat_mode_active = false
		if _current_direction != Vector2.ZERO:
			_try_move()

	if _movement_cooldown > 0.0:
		_movement_cooldown -= delta

	if _player_movement_component.data != null and _player_movement_component.data.is_moving:
		return

	if _current_direction != Vector2.ZERO and _movement_cooldown <= 0.0:
		_hold_timer += delta
		if not _repeat_mode_active and _hold_timer >= _player_input_component.data.initial_repeat_delay:
			_repeat_mode_active = true
			_hold_timer = 0.0
			_try_move()
		elif _repeat_mode_active and _hold_timer >= _player_input_component.data.repeat_rate:
			_hold_timer = 0.0
			_try_move()

func _get_dominant_direction(vector: Vector2) -> Vector2:
	if vector == Vector2.ZERO:
		return Vector2.ZERO

	if abs(vector.x) > abs(vector.y):
		var vector_sign: float = sign(vector.x)
		return Vector2(vector_sign, 0.0)
	else:
		var vector_sign: float = sign(vector.y)
		return Vector2(0.0, vector_sign)


func _try_move() -> void:
	if _player_movement_component.data == null or _player_input_component.data == null:
		return

	if not _player_movement_component.data.is_moving and _movement_cooldown <= 0.0 and _current_direction != Vector2.ZERO:
		movement_system.move(_player_movement_component, _current_direction)
		_movement_cooldown = _player_input_component.data.repeat_rate


func _get_inversion_multiplier() -> float:
	if not is_instance_valid(_player_ability_component):
		return 1.0

	for ability: AbilityData in _player_ability_component.abilities:
		if ability is InvertedControlsAbilityData:
			return -1.0

	return 1.0
