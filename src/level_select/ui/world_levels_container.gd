extends HBoxContainer

@export var world_data: WorldData

func _ready() -> void:
	var texture_buttons: Array[LevelIcon] = []
	for child in get_children():
		if child is LevelIcon:
			texture_buttons.append(child as LevelIcon)

	for i in range(world_data.levels.size()):
		var is_unlocked: bool = world_data.levels[i].unlocked_by_level_id.is_empty() or GameProgress.is_level_completed(world_data.levels[i].unlocked_by_level_id)
		texture_buttons[i].setup(world_data.levels[i], is_unlocked)
		texture_buttons[i].level_selected.connect(_on_level_selected)

func _on_level_selected(scene_path: String) -> void:
	get_tree().change_scene_to_file(scene_path)
