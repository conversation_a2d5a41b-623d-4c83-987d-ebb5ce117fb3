[gd_scene load_steps=5 format=3 uid="uid://c7hppsi5r3ka1"]

[ext_resource type="PackedScene" uid="uid://gjocywalbo15" path="res://src/level_select/ui/level_icon.tscn" id="1_niumu"]
[ext_resource type="Script" uid="uid://d0owgqfk2y7wj" path="res://src/level_select/ui/world_levels_container.gd" id="1_rn4oy"]
[ext_resource type="Resource" uid="uid://biwgbyo63pu5v" path="res://src/level_select/data/world_1.tres" id="2_cma7i"]
[ext_resource type="Texture2D" uid="uid://da3x0kkx4t6dj" path="res://icon.svg" id="2_rn4oy"]

[node name="WorldLevelsContainer" type="HBoxContainer"]
alignment = 1
script = ExtResource("1_rn4oy")
world_data = ExtResource("2_cma7i")

[node name="LevelIcon" parent="." instance=ExtResource("1_niumu")]
layout_mode = 2
texture_normal = ExtResource("2_rn4oy")

[node name="Divider" type="ColorRect" parent="."]
custom_minimum_size = Vector2(100, 20)
layout_mode = 2
size_flags_vertical = 4

[node name="LevelIcon2" parent="." instance=ExtResource("1_niumu")]
layout_mode = 2
texture_normal = ExtResource("2_rn4oy")

[node name="Divider2" type="ColorRect" parent="."]
custom_minimum_size = Vector2(100, 20)
layout_mode = 2
size_flags_vertical = 4

[node name="LevelIcon3" parent="." instance=ExtResource("1_niumu")]
layout_mode = 2
texture_normal = ExtResource("2_rn4oy")

[node name="Divider3" type="ColorRect" parent="."]
custom_minimum_size = Vector2(100, 20)
layout_mode = 2
size_flags_vertical = 4

[node name="LevelIcon4" parent="." instance=ExtResource("1_niumu")]
layout_mode = 2
texture_normal = ExtResource("2_rn4oy")
